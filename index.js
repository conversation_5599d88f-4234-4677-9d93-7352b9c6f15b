const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')
const cors = require('cors')
const hpp = require('hpp')
const { SpeechClient } = require('@google-cloud/speech')
const config = require('./modules/config')
const { createAIService, generateAIReply } = require('./modules/AIService')
const { generateSpeech } = require('./modules/voiceService')
const { customLog } = require('./modules/logger')
const { cleanTextForSpeech } = require('./modules/textProcessor')
const { setSecurityHeaders, validateApiKey, configureSocketSecurity, configureCors } = require('./modules/security')
const cookieParser = require('cookie-parser')
const csurf = require('csurf')
const csrfProtection = csurf({ cookie: true })

require('better-logging')(console)

// Error handling utilities
const handleAsyncError = (fn, context = 'Unknown') => async (...args) => {
  try {
    return await fn(...args)
  } catch (error) {
    customLog(`Async error in ${context}:`, error.message || error)
    throw error
  }
}

// Input validation functions
const validateAudioBuffer = (audioBuffer) => {
  if (!audioBuffer) {
    throw new Error('Audio buffer is required')
  }

  if (typeof audioBuffer !== 'object') {
    throw new Error('Invalid audio buffer format')
  }

  const maxSize = 1024 * 1024 // 1MB limit
  if (audioBuffer.byteLength && audioBuffer.byteLength > maxSize) {
    throw new Error(`Audio buffer too large: ${audioBuffer.byteLength} bytes (max: ${maxSize})`)
  }

  return true
}

const validateTranscription = (transcription) => {
  if (typeof transcription !== 'string') {
    throw new Error('Transcription must be a string')
  }

  if (transcription.length > config.MAX_USER_INPUT_LENGTH) {
    customLog(`Warning: Truncating long user input from ${transcription.length} to ${config.MAX_USER_INPUT_LENGTH} characters`)
    return transcription.substring(0, config.MAX_USER_INPUT_LENGTH)
  }

  return transcription.trim()
}

const validateConversationHistory = (history) => {
  if (!Array.isArray(history)) {
    throw new Error('Conversation history must be an array')
  }

  // Limit conversation history to prevent memory issues
  const maxHistoryLength = 50
  if (history.length > maxHistoryLength) {
    customLog(`Warning: Trimming conversation history from ${history.length} to ${maxHistoryLength} entries`)
    return history.slice(-maxHistoryLength)
  }

  return history
}

const app = express()
app.disable('x-powered-by')
const httpServer = createServer(app)

setSecurityHeaders(app)

app.use(hpp())
app.use(express.json())
configureCors(app)
app.use(cookieParser())
app.use(csrfProtection)

app.use('/api', validateApiKey)

const io = new Server(httpServer, {
  cors: {
    origin:
      process.env.NODE_ENV === 'production'
        ? [process.env.FRONTEND_DEV_URL, process.env.FRONTEND_PRO_URL]
        : ['http://localhost:3000', 'http://localhost:5173'],
    methods: ['GET', 'POST'],
    credentials: true
  }
})
configureSocketSecurity(io)

createAIService()
const client = new SpeechClient()

io.on('connection', (socket) => {
  customLog('Client connected:', socket.id)

  let recognizeStream = null
  let conversationHistory = []
  let isMuted = false
  let isStreamActive = true
  let inactivityTimeout = null

  const startRecognitionStream = () => {
    try {
      // Clean up existing stream
      if (recognizeStream && !recognizeStream.destroyed) {
        try {
          recognizeStream.removeAllListeners()
          recognizeStream.end()
        } catch (cleanupError) {
          customLog('Error cleaning up previous stream:', cleanupError.message)
        }
      }

      customLog('Starting new recognition stream for client:', socket.id)
      isStreamActive = true

      // Validate speech client
      if (!client) {
        throw new Error('Speech client not initialized')
      }

      // Validate configuration
      if (!config.SPEECH_TO_TEXT_CONFIG) {
        throw new Error('Speech-to-text configuration not found')
      }

      const stream = client
        .streamingRecognize(config.SPEECH_TO_TEXT_CONFIG)
        .on('error', (error) => {
          customLog('Error transcribing audio for client', socket.id, ':', error.message || error)
          isStreamActive = false

          // Clean up stream on error
          if (recognizeStream) {
            try {
              if (!recognizeStream.destroyed) {
                recognizeStream.removeAllListeners()
                recognizeStream.end()
              }
            } catch (cleanupError) {
              customLog('Error cleaning up stream after error:', cleanupError.message)
            } finally {
              recognizeStream = null
            }
          }

          // Notify client of transcription error
          socket.emit('error', {
            message: 'Speech recognition temporarily unavailable. Please try again.',
            type: 'transcription_error'
          })
        })
        .on('data', async (data) => {
        try {
          if (data.results[0] && data.results[0].alternatives[0]) {
            resetInactivityTimer()

            let transcription = data.results[0].alternatives[0].transcript.trim()
            socket.emit('transcription', transcription)

            if (data.results[0].isFinal && transcription && transcription !== '') {
              socket.emit('loading')

              if (transcription.toLowerCase() === 'ok aura') {
                socket.emit('reply', { text: '[OK AURA]', audioUrl: null })
              } else {
                try {
                  // Process transcription
                  if (transcription.toLowerCase().startsWith('ok aura')) {
                    transcription = transcription.slice(7).trim()
                  }

                  // Validate and sanitize transcription
                  transcription = validateTranscription(transcription)

                  // Validate conversation history before adding new entry
                  conversationHistory = validateConversationHistory(conversationHistory)
                  conversationHistory.push({ user: true, content: transcription })
                  customLog(`User: ${transcription}`)

                  // Generate AI reply with error handling
                  const reply = await handleAsyncError(
                    () => generateAIReply(conversationHistory),
                    'AI Reply Generation'
                  )()

                  if (!reply) {
                    throw new Error('AI service returned empty response')
                  }

                  customLog(`AI: ${reply}`)
                  conversationHistory.push({ user: false, content: reply })

                  // Check if reply requires audio generation
                  if (
                    reply.startsWith('[JSON]') ||
                    reply.startsWith('[NONE]') ||
                    reply.startsWith('[COMMAND]') ||
                    reply.startsWith('[PAUSE]') ||
                    reply.startsWith('[EXIT]')
                  ) {
                    socket.emit('reply', { text: reply, audioUrl: null })
                  } else {
                    let audioUrl = null

                    if (!isMuted) {
                      try {
                        // Generate speech with error handling
                        const audioBuffer = await handleAsyncError(
                          () => generateSpeech(cleanTextForSpeech(reply)),
                          'Speech Generation'
                        )()

                        if (audioBuffer && audioBuffer.length > 0) {
                          audioUrl = `data:audio/mpeg;base64,${audioBuffer.toString('base64')}`
                        } else {
                          customLog('Warning: Speech generation returned empty buffer')
                        }
                      } catch (speechError) {
                        customLog('Speech generation failed, sending text-only response:', speechError.message)
                        // Continue without audio - don't fail the entire response
                      }
                    }

                    socket.emit('reply', { text: reply, audioUrl })
                  }
                } catch (processingError) {
                  customLog('Error processing transcription:', processingError.message)
                  socket.emit('error', {
                    message: 'Error processing your request. Please try again.',
                    type: 'processing_error'
                  })
                }
              }
            }
          }
        } catch (dataError) {
          customLog('Error processing speech data:', dataError.message)
          socket.emit('error', {
            message: 'Error processing audio data. Please try again.',
            type: 'data_processing_error'
          })
        }
      })

      return stream
    } catch (streamError) {
      customLog('Error creating recognition stream:', streamError.message)
      isStreamActive = false
      socket.emit('error', {
        message: 'Failed to initialize speech recognition. Please try again.',
        type: 'stream_initialization_error'
      })
      return null
    }
  }

  const resetInactivityTimer = () => {
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }

    inactivityTimeout = setTimeout(() => {
      if (recognizeStream && !recognizeStream.destroyed) {
        customLog('Closing stream due to inactivity')
        recognizeStream.end()
        recognizeStream = null
        isStreamActive = false
      }
    }, 60000) // 1 minute
  }

  socket.on('audio', (audioBuffer) => {
    try {
      // Validate audio buffer input
      validateAudioBuffer(audioBuffer)

      if (!recognizeStream || !isStreamActive) {
        recognizeStream = startRecognitionStream()
      }

      const buffer = Buffer.from(new Uint8Array(audioBuffer))

      // Validate buffer size after conversion
      if (buffer.length === 0) {
        customLog('Warning: Received empty audio buffer')
        return
      }

      try {
        recognizeStream.write(buffer)
        resetInactivityTimer()
      } catch (streamError) {
        customLog('Error writing to stream:', streamError.message)

        // Attempt to restart stream and retry once
        try {
          recognizeStream = startRecognitionStream()
          recognizeStream.write(buffer)
          resetInactivityTimer()
        } catch (retryError) {
          customLog('Failed to restart stream and retry:', retryError.message)
          socket.emit('error', {
            message: 'Audio processing temporarily unavailable. Please try again.',
            type: 'stream_error'
          })
        }
      }
    } catch (validationError) {
      customLog('Audio validation error:', validationError.message)
      socket.emit('error', {
        message: 'Invalid audio data received.',
        type: 'validation_error'
      })
    }
  })

  socket.on('disconnect', (reason) => {
    try {
      customLog('Client disconnected:', socket.id, 'Reason:', reason)

      // Clean up recognition stream
      if (recognizeStream) {
        try {
          if (!recognizeStream.destroyed) {
            recognizeStream.removeAllListeners()
            recognizeStream.end()
          }
        } catch (streamError) {
          customLog('Error cleaning up recognition stream:', streamError.message)
        } finally {
          recognizeStream = null
        }
      }

      // Clean up timers
      if (inactivityTimeout) {
        clearTimeout(inactivityTimeout)
        inactivityTimeout = null
      }

      // Clean up conversation history to free memory
      conversationHistory = []

    } catch (error) {
      customLog('Error during disconnect cleanup:', error.message)
    }
  })

  socket.on('mute-sound', (data) => {
    try {
      // Validate that no unexpected data is sent
      if (data !== undefined && data !== null) {
        customLog(`Warning: Unexpected data in mute-sound event from ${socket.id}:`, data)
      }

      isMuted = true
      customLog(`Sound muted for client: ${socket.id}`)
    } catch (error) {
      customLog('Error handling mute-sound event:', error.message)
      socket.emit('error', {
        message: 'Error processing mute request.',
        type: 'mute_error'
      })
    }
  })

  socket.on('unmute-sound', (data) => {
    try {
      // Validate that no unexpected data is sent
      if (data !== undefined && data !== null) {
        customLog(`Warning: Unexpected data in unmute-sound event from ${socket.id}:`, data)
      }

      isMuted = false
      customLog(`Sound unmuted for client: ${socket.id}`)
    } catch (error) {
      customLog('Error handling unmute-sound event:', error.message)
      socket.emit('error', {
        message: 'Error processing unmute request.',
        type: 'unmute_error'
      })
    }
  })

  recognizeStream = startRecognitionStream()
  resetInactivityTimer()
})

httpServer.listen(config.PORT, () => {
  customLog(`Server running on port ${config.PORT}`)
})
